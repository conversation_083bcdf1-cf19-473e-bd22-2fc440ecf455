import React, { useState, useEffect, useRef } from 'react'
import './Terminal.css'
import { commands } from '../utils/commands.js'



const Terminal = () => {
  const [input, setInput] = useState('')
  const [output, setOutput] = useState([])
  const [commandHistory, setCommandHistory] = useState([])
  const [historyIndex, setHistoryIndex] = useState(-1)
  const [isTyping, setIsTyping] = useState(false)
  const inputRef = useRef(null)
  const terminalRef = useRef(null)

  // Welcome message
  useEffect(() => {
    const welcomeMessage = [
      { type: 'success', content: '╔══════════════════════════════════════════════════╗' },
      { type: 'success', content: '║        Welcome to Pratham Sharda\'s Portfolio      ║' },
      { type: 'success', content: '║              Terminal Interface v1.0             ║' },
      { type: 'success', content: '╚══════════════════════════════════════════════════╝' },
      { type: 'info', content: '' },
      { type: 'info', content: '🚀 Interactive Portfolio Terminal' },
      { type: 'info', content: '👈 Try dragging the 3D ID card on the left!' },
      { type: 'info', content: '💻 Type "help" to see available commands' },
      { type: 'info', content: '🎯 Try "about" to learn more about Pratham Sharda' },
      { type: 'info', content: '✨ Use Tab for autocomplete, ↑/↓ for history' },
      { type: 'info', content: '' },
      { type: 'output', content: 'System initialized. Ready for commands...' },
      { type: 'info', content: '─'.repeat(50) }
    ]
    setOutput(welcomeMessage)
  }, [])

  // Auto-focus input and scroll to bottom
  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus()
    }
    if (terminalRef.current) {
      terminalRef.current.scrollTop = terminalRef.current.scrollHeight
    }
  }, [output])

  // Handle command execution
  const executeCommand = async (command) => {
    const trimmedCommand = command.trim()
    const commandLower = trimmedCommand.toLowerCase()

    // Add command to history
    if (trimmedCommand && !commandHistory.includes(trimmedCommand)) {
      setCommandHistory(prev => [...prev, trimmedCommand])
    }

    // Add command to output
    setOutput(prev => [...prev, { type: 'command', content: command }])

    // Handle special commands
    if (commandLower === 'clear') {
      setOutput([])
      return
    }

    // Execute command (preserve original case for arguments)
    const result = await commands.execute(trimmedCommand)
    
    if (result.type === 'typing') {
      setIsTyping(true)
      await typeMessage(result.content)
      setIsTyping(false)
    } else {
      setOutput(prev => [...prev, result])
    }
  }

  // Typing effect for special commands
  const typeMessage = (message) => {
    return new Promise((resolve) => {
      let index = 0
      const lines = Array.isArray(message) ? message : [message]
      
      const typeNextLine = () => {
        if (index < lines.length) {
          const line = lines[index]
          setOutput(prev => [...prev, { type: 'typing', content: '', id: Date.now() + index }])
          
          let charIndex = 0
          const typeChar = () => {
            if (charIndex < line.length) {
              setOutput(prev => {
                const newOutput = [...prev]
                const lastItem = newOutput[newOutput.length - 1]
                if (lastItem.type === 'typing') {
                  lastItem.content = line.substring(0, charIndex + 1)
                }
                return newOutput
              })
              charIndex++
              setTimeout(typeChar, 20)
            } else {
              // Convert typing to regular output
              setOutput(prev => {
                const newOutput = [...prev]
                const lastItem = newOutput[newOutput.length - 1]
                if (lastItem.type === 'typing') {
                  lastItem.type = 'success'
                }
                return newOutput
              })
              index++
              setTimeout(typeNextLine, 100)
            }
          }
          typeChar()
        } else {
          resolve()
        }
      }
      typeNextLine()
    })
  }

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault()
    if (input.trim() && !isTyping) {
      executeCommand(input)
      setInput('')
      setHistoryIndex(-1)
    }
  }

  // Handle key navigation
  const handleKeyDown = (e) => {
    // Allow default behavior for most keys to enable natural terminal experience
    if (e.key === 'ArrowUp') {
      e.preventDefault()
      if (commandHistory.length > 0) {
        const newIndex = historyIndex === -1 ? commandHistory.length - 1 : Math.max(0, historyIndex - 1)
        setHistoryIndex(newIndex)
        setInput(commandHistory[newIndex])
      }
    } else if (e.key === 'ArrowDown') {
      e.preventDefault()
      if (historyIndex !== -1) {
        const newIndex = historyIndex + 1
        if (newIndex >= commandHistory.length) {
          setHistoryIndex(-1)
          setInput('')
        } else {
          setHistoryIndex(newIndex)
          setInput(commandHistory[newIndex])
        }
      }
    } else if (e.key === 'Tab') {
      e.preventDefault()
      // Enhanced autocomplete
      const availableCommands = [
        'help', 'about', 'skills', 'projects', 'experience', 'education',
        'contact', 'resume', 'social', 'clear', 'echo', 'whoami', 'date',
        'pwd', 'ls', 'cat', 'neofetch', 'pokedex'
      ]
      const matches = availableCommands.filter(cmd => cmd.startsWith(input.toLowerCase()))
      if (matches.length === 1) {
        setInput(matches[0])
      } else if (matches.length > 1) {
        // Show available matches
        setOutput(prev => [...prev,
          { type: 'command', content: input },
          { type: 'info', content: `Available completions: ${matches.join(', ')}` }
        ])
      }
    }
  }

  return (
    <div className="terminal-container" onClick={() => inputRef.current?.focus()}>
      <div className="terminal-header">
        <div className="terminal-buttons">
          <span className="terminal-button close"></span>
          <span className="terminal-button minimize"></span>
          <span className="terminal-button maximize"></span>
        </div>
        <div className="terminal-title">pstorque:~$</div>
      </div>
      
      <div className="terminal-body" ref={terminalRef}>
        <div className="terminal-output">
          {output.map((line, index) => (
            <div key={index} className={`terminal-line ${line.type}`}>
              {line.type === 'command' && <span className="prompt">pratham:~$</span>}
              {line.isComponent ? (
                <div className="content">{line.content}</div>
              ) : line.isHtml ? (
                <div className="content" dangerouslySetInnerHTML={{ __html: line.content }} />
              ) : (
                <span className="content">{line.content}</span>
              )}
              {line.type === 'typing' && <span className="cursor">█</span>}
            </div>
          ))}
        </div>
        
        <form onSubmit={handleSubmit} className="terminal-input-form">
          <div className="terminal-input-line">
            <span className="prompt">pratham:~$</span>
            <div className="input-container">
              <span className="input-text">{input}</span>
              <span className="cursor blinking">█</span>
              <input
                ref={inputRef}
                type="text"
                value={input}
                onChange={(e) => setInput(e.target.value)}
                onKeyDown={handleKeyDown}
                className="terminal-input"
                disabled={isTyping}
                autoComplete="off"
                spellCheck="false"
                autoFocus
              />
            </div>
          </div>
        </form>
      </div>
    </div>
  )
}

export default Terminal
