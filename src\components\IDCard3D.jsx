import React, { useRef, useState, useEffect } from 'react'
import { <PERSON><PERSON>, extend, useThree, use<PERSON><PERSON><PERSON>, useLoader } from '@react-three/fiber'
import { Environment, Text, Center, RoundedBox } from '@react-three/drei'
import { Physics, RigidBody, BallCollider, CuboidCollider, useRopeJoint, useSphericalJoint } from '@react-three/rapier'
import { MeshLineGeometry, MeshLineMaterial } from 'meshline'
import * as THREE from 'three'
import './IDCard3D.css'
import { FlakesTexture } from 'three/examples/jsm/Addons.js'

// Extend the catalog to use MeshLine
extend({ MeshLineGeometry, MeshLineMaterial })

// Profile Image Component
const ProfileImage = ({ position }) => {
  const texture = useLoader(THREE.TextureLoader, '/profilePicture.png')

  return (
    <mesh position={position}>
      <circleGeometry args={[0.28, 32]} />
      <meshPhysicalMaterial
        map={texture}
        roughness={0.3}
        envMapIntensity={0.2}
        clearcoat={0.1}
        clearcoatRoughness={0.8}
      />
    </mesh>
  )
}

// Barcode Component
const Barcode = ({ position }) => {
  const barcodePattern = [
    0.008, 0.004, 0.008, 0.004, 0.012, 0.004, 0.008, 0.004, 0.008, 0.004,
    0.012, 0.004, 0.008, 0.004, 0.008, 0.004, 0.012, 0.004, 0.008, 0.004,
    0.008, 0.004, 0.012, 0.004, 0.008, 0.004, 0.008, 0.004, 0.012, 0.004,
    0.008, 0.004, 0.008, 0.004, 0.012, 0.004, 0.008, 0.004, 0.008, 0.004
  ]

  let currentX = -0.6

  return (
    <group position={position}>
      {barcodePattern.map((width, index) => {
        const isBar = index % 2 === 0
        const barPosition = [currentX + width / 2, 0, 0]
        currentX += width

        if (!isBar) return null

        return (
          <mesh key={index} position={barPosition}>
            <boxGeometry args={[width, 0.15, 0.001]} />
            <meshPhysicalMaterial
              color="#000000"
              metalness={0.0}
              roughness={0.9}
              envMapIntensity={0.05}
            />
          </mesh>
        )
      })}
    </group>
  )
}

// Create a custom scalloped border geometry
const createScallopedBorder = (width, height, scallops = 16) => {
  const shape = new THREE.Shape()
  const halfWidth = width / 2
  const halfHeight = height / 2
  
  // Create scalloped edges
  const sides = [
    // Top edge
    { start: [-halfWidth, halfHeight], end: [halfWidth, halfHeight], normal: [0, 1] },
    // Right edge
    { start: [halfWidth, halfHeight], end: [halfWidth, -halfHeight], normal: [1, 0] },
    // Bottom edge
    { start: [halfWidth, -halfHeight], end: [-halfWidth, -halfHeight], normal: [0, -1] },
    // Left edge
    { start: [-halfWidth, -halfHeight], end: [-halfWidth, halfHeight], normal: [-1, 0] }
  ]
  
  shape.moveTo(-halfWidth, halfHeight)
  
  sides.forEach(side => {
    const { start, end, normal } = side
    const length = Math.sqrt(Math.pow(end[0] - start[0], 2) + Math.pow(end[1] - start[1], 2))
    const scallopSize = length / scallops
    const scallopDepth = 0.008
    
    for (let i = 0; i < scallops; i++) {
      const t1 = i / scallops
      const t2 = (i + 0.5) / scallops
      const t3 = (i + 1) / scallops
      
      const x1 = start[0] + (end[0] - start[0]) * t1
      const y1 = start[1] + (end[1] - start[1]) * t1
      
      const x2 = start[0] + (end[0] - start[0]) * t2
      const y2 = start[1] + (end[1] - start[1]) * t2
      
      const x3 = start[0] + (end[0] - start[0]) * t3
      const y3 = start[1] + (end[1] - start[1]) * t3
      
      // Create scallop (inward curve)
      const controlX = x2 + normal[0] * scallopDepth
      const controlY = y2 + normal[1] * scallopDepth
      
      shape.lineTo(x1, y1)
      shape.quadraticCurveTo(controlX, controlY, x3, y3)
    }
  })
  
  shape.closePath()
  return shape
}

// Holographic Stamp Component
const HolographicStamp = ({ position }) => {
  const meshRef = useRef()
  const patternRef = useRef()
  const overlayRef = useRef()

  useFrame((state) => {
    if (meshRef.current) {
      // Create intense rainbow holographic effect
      const time = state.clock.elapsedTime
      // Fast rainbow cycling for vibrant effect
      meshRef.current.material.color.setHSL(
        (time * 1.5) % 1, // Much faster hue cycle
        1.0, // Maximum saturation
        0.8  // Bright lightness
      )

      // Dynamic iridescence thickness for shifting patterns
      meshRef.current.material.iridescenceThicknessRange = [
        100 + Math.sin(time * 2) * 200,
        800 + Math.cos(time * 1.5) * 400
      ]
    }

    if (patternRef.current) {
      // Faster pattern animation with opposite direction
      const time = state.clock.elapsedTime
      patternRef.current.material.color.setHSL(
        ((time * -2.0) + 0.33) % 1, // Reverse direction, faster speed
        1.0,
        0.9
      )

      // Dynamic iridescence for pattern layer
      patternRef.current.material.iridescenceThicknessRange = [
        200 + Math.cos(time * 3) * 150,
        600 + Math.sin(time * 2.5) * 300
      ]
    }

    if (overlayRef.current) {
      // Third layer for maximum rainbow effect
      const time = state.clock.elapsedTime
      overlayRef.current.material.color.setHSL(
        ((time * 2.5) + 0.66) % 1, // Even faster, different offset
        1.0,
        0.7
      )
    }
  })

  return (
    <group position={position}>
      {/* Main holographic square background */}
      <mesh ref={meshRef}>
        <planeGeometry args={[0.25, 0.18]} />
        <meshPhysicalMaterial
          color="#ff0080"
          metalness={1.0}
          roughness={0.0}
          envMapIntensity={5.0}
          clearcoat={1.0}
          clearcoatRoughness={0.0}
          transmission={0.2}
          transparent={true}
          opacity={1.0}
          iridescence={1.0}
          iridescenceIOR={2.0}
          iridescenceThicknessRange={[100, 1200]}
          side={THREE.DoubleSide}
        />
      </mesh>

      {/* Second holographic layer */}
      <mesh ref={patternRef} position={[0, 0, 0.001]}>
        <planeGeometry args={[0.23, 0.16]} />
        <meshPhysicalMaterial
          color="#00ff80"
          metalness={1.0}
          roughness={0.0}
          envMapIntensity={4.0}
          transparent={true}
          opacity={0.7}
          iridescence={1.0}
          iridescenceIOR={2.5}
          iridescenceThicknessRange={[200, 800]}
          side={THREE.DoubleSide}
        />
      </mesh>

      {/* Third holographic layer for maximum rainbow effect */}
      <mesh ref={overlayRef} position={[0, 0, 0.002]}>
        <planeGeometry args={[0.21, 0.14]} />
        <meshPhysicalMaterial
          color="#8000ff"
          metalness={1.0}
          roughness={0.0}
          envMapIntensity={3.5}
          transparent={true}
          opacity={0.5}
          iridescence={1.0}
          iridescenceIOR={3.0}
          iridescenceThicknessRange={[300, 1000]}
          side={THREE.DoubleSide}
        />
      </mesh>

      {/* Border frame with scalloped edges */}
      <mesh position={[0, 0, 0.002]}>
        <shapeGeometry args={[createScallopedBorder(0.26, 0.19, 12)]} />
        <meshPhysicalMaterial
          color="#ffffff"
          metalness={0.9}
          roughness={0.1}
          transparent={true}
          opacity={0.8}
        />
      </mesh>

      {/* Inner border with scalloped edges */}
      <mesh position={[0, 0, 0.003]}>
        <shapeGeometry args={[createScallopedBorder(0.24, 0.17, 12)]} />
        <meshPhysicalMaterial
          color="#000000"
          metalness={0.0}
          roughness={1.0}
          transparent={true}
          opacity={0.1}
        />
      </mesh>

      {/* PSTORQUE text */}
      <Text
        position={[0, 0, 0.004]}
        fontSize={0.058}
        color="#ffffff"
        anchorX="center"
        anchorY="middle"
        fontWeight={700}
        //border
        outlineWidth={0.005}
        outlineColor="#736b6bff"
        outlineOpacity={0.3}
       
      >
        Verified
      </Text>
    </group>
  )
}

// ID Card Component
const IDCard = ({ position, rotation, ...props }) => {
  return (
    <group position={position} rotation={rotation} {...props}>
      {/* Card Base - Dark plastic card with subtle reflections and rounded corners */}
      <RoundedBox args={[1.6, 2.25, 0.03]} radius={0.08} smoothness={4}>
        <meshPhysicalMaterial
          color="#c60f0f"
          roughness={0.9}
          envMapIntensity={0.05}
        />
      </RoundedBox>

      {/* Card Border - Dark plastic edge with rounded corners */}
      <RoundedBox args={[1.62, 2.27, 0.031]} radius={0.08} smoothness={4}>
        <meshPhysicalMaterial
          color="#877f7fff"
          metalness={0.5}
          roughness={0.7}
          envMapIntensity={0.95}
          clearcoat={0.3}
          clearcoatRoughness={0.2}
          transmission={0.5}
        />
      </RoundedBox>

      {/* Card Content */}
      <group position={[0, 0, 0.016]}>
        {/* Clean card surface - no overlapping texture lines */}

        {/* Header Section - Dark plastic branding area */}
        <mesh position={[0, 0.9, 0.001]}>
          <planeGeometry args={[1.5, 0.3]} />
          <meshPhysicalMaterial
            color="#000000"
            metalness={0.8}
          />
        </mesh>

        {/* Company Logo/Brand Text */}
        <Text
          position={[0, 0.9, 0.002]}
          fontSize={0.1}
          color="#e2d1d15a"
          anchorX="center"
          anchorY="middle"
          fontWeight={700}
        >
          PS Torque
        </Text>

        {/* Divider line under header */}
        <mesh position={[0, 0.72, 0.002]}>
          <planeGeometry args={[1.4, 0.001]} />
          <meshPhysicalMaterial
            color="#333333"
          />
        </mesh>

        {/* Employee Information Section */}
        <mesh position={[0, 0.45, 0.001]}>
          <planeGeometry args={[1.4, 0.4]} />
          <meshPhysicalMaterial
            color="#000000"
            metalness={0.8}
            roughness={0}
            envMapIntensity={0.72}
            clearcoat={0.25}
            clearcoatRoughness={0.9}
          />
        </mesh>

        {/* Name */}
        <Text
          position={[0, 0.5, 0.002]}
          fontSize={0.09}
          color="#ffffff"
          anchorX="center"
          anchorY="middle"
        >
          PRATHAM SHARDA
        </Text>

        {/* Title */}
        <Text
          position={[0, 0.38, 0.002]}
          fontSize={0.06}
          color="#cccccc"
          anchorX="center"
          anchorY="middle"
        >
          Software Developer
        </Text>

        {/* Profile Image Background - Dark plastic ring */}
        <mesh position={[0, -0.05, 0.002]}>
          <circleGeometry args={[0.31, 32]} />
          <meshPhysicalMaterial
            color="#3a3a3a"
            metalness={0.2}
            roughness={0.6}
            clearcoat={0.3}
          />
        </mesh>

        {/* Profile Image */}
        <ProfileImage position={[0, -0.05, 0.003]} />

        {/* Footer Section - Dark plastic ID and Details */}
        <mesh position={[0, -0.6, 0.001]}>
          <planeGeometry args={[1.4, 0.4]} />
          <meshPhysicalMaterial
            color="#0f0f0f"
            metalness={0.9}
            roughness={0.0}
            envMapIntensity={0.15}
            clearcoat={0.3}
            clearcoatRoughness={0.5}
          />
        </mesh>

        {/* ID Number */}
        <Text
          position={[0, -0.55, 0.002]}
          fontSize={0.09}
          color="#e0e0e0"
          anchorX="center"
          anchorY="middle"
        >
          ID: 08112004
        </Text>

        {/* Number */}
        <Text
          position={[0, -0.68, 0.002]}
          fontSize={0.06}
          color="#aaaaaa"
          anchorX="center"
          anchorY="middle"
          fontWeight={700}
        >
          +91 98795 91447
        </Text>

        {/* Divider line above footer */}
        <mesh position={[0, -0.38, 0.002]}>
          <planeGeometry args={[1.4, 0.001]} />
          <meshPhysicalMaterial
            color="#171717f5"
            metalness={0.9}
            roughness={0.8}
            envMapIntensity={0.9}
            clearcoat={0.2}
            clearcoatRoughness={0.6}
          />
        </mesh>

        {/* Barcode at bottom */}
        <Barcode position={[0, -0.95, 0.002]} />

        {/* Holographic stamp beside barcode on bottom right */}
        <HolographicStamp position={[0.5, -0.95, 0.003]} />

      </group>
    </group>
  )
}

// Physics-based Lanyard Band Component
const LanyardBand = ({ maxSpeed = 50, minSpeed = 10 }) => {
  const band = useRef()
  const fixed = useRef()
  const j1 = useRef()
  const j2 = useRef()
  const j3 = useRef()
  const card = useRef()
  
  const vec = new THREE.Vector3()
  const ang = new THREE.Vector3()
  const rot = new THREE.Vector3()
  const dir = new THREE.Vector3()
  
  const segmentProps = {
    type: 'dynamic',
    canSleep: true,
    colliders: false,
    angularDamping: 2,
    linearDamping: 2
  }
  
  const { width, height } = useThree((state) => state.size)
  const [curve] = useState(() => new THREE.CatmullRomCurve3([
    new THREE.Vector3(), new THREE.Vector3(), new THREE.Vector3(), new THREE.Vector3()
  ]))
  const [dragged, drag] = useState(false)
  const [hovered, hover] = useState(false)

  // Create rope joints
  useRopeJoint(fixed, j1, [[0, 0, 0], [0, 0, 0], 0.5])
  useRopeJoint(j1, j2, [[0, 0, 0], [0, 0, 0], 0.5])
  useRopeJoint(j2, j3, [[0, 0, 0], [0, 0, 0], 0.5])
  useSphericalJoint(j3, card, [[0, 0, 0], [0, 0.7, 0]])

  useEffect(() => {
    if (hovered) {
      document.body.style.cursor = dragged ? 'grabbing' : 'grab'
      return () => void (document.body.style.cursor = 'auto')
    }
  }, [hovered, dragged])

  useFrame((state, delta) => {
    if (dragged) {
      vec.set(state.pointer.x, state.pointer.y, 0.5).unproject(state.camera)
      dir.copy(vec).sub(state.camera.position).normalize()
      vec.add(dir.multiplyScalar(state.camera.position.length()))
      ;[card, j1, j2, j3, fixed].forEach((ref) => ref.current?.wakeUp())
      card.current?.setNextKinematicTranslation({
        x: vec.x - dragged.x,
        y: vec.y - dragged.y,
        z: vec.z - dragged.z
      })
    }
    
    if (fixed.current) {
      // Fix jitter when over pulling the card
      ;[j1, j2].forEach((ref) => {
        if (!ref.current.lerped) {
          ref.current.lerped = new THREE.Vector3().copy(ref.current.translation())
        }
        const clampedDistance = Math.max(0.1, Math.min(1, ref.current.lerped.distanceTo(ref.current.translation())))
        ref.current.lerped.lerp(ref.current.translation(), delta * (minSpeed + clampedDistance * (maxSpeed - minSpeed)))
      })
      
      // Calculate catmull curve
      curve.points[0].copy(j3.current.translation())
      curve.points[1].copy(j2.current.lerped)
      curve.points[2].copy(j1.current.lerped)
      curve.points[3].copy(fixed.current.translation())
      band.current.geometry.setPoints(curve.getPoints(32))
      
      // Tilt it back towards the screen
      ang.copy(card.current.angvel())
      rot.copy(card.current.rotation())
      card.current.setAngvel({ x: ang.x, y: ang.y - rot.y * 0.25, z: ang.z })
    }
  })

  curve.curveType = 'chordal'

  return (
    <>
      <group position={[0, 4, 0]}>
        <RigidBody ref={fixed} {...segmentProps} type="fixed" />
        <RigidBody position={[0, -0.5, 0]} ref={j1} {...segmentProps}>
          <BallCollider args={[0.1]} />
        </RigidBody>
        <RigidBody position={[0, -1, 0]} ref={j2} {...segmentProps}>
          <BallCollider args={[0.1]} />
        </RigidBody>
        <RigidBody position={[0, -1.5, 0]} ref={j3} {...segmentProps}>
          <BallCollider args={[0.1]} />
        </RigidBody>
        <RigidBody 
          position={[2, 0, 0]} 
          ref={card} 
          {...segmentProps} 
          type={dragged ? 'kinematicPosition' : 'dynamic'}
        >
          <CuboidCollider args={[0.45, 0.64, 0.01]} />
          <group
            scale={1.125}
            position={[0, -0.6, -0.05]}
            onPointerOver={() => hover(true)}
            onPointerOut={() => hover(false)}
            onPointerUp={(e) => (e.target.releasePointerCapture(e.pointerId), drag(false))}
            onPointerDown={(e) => (
              e.target.setPointerCapture(e.pointerId),
              drag(new THREE.Vector3().copy(e.point).sub(vec.copy(card.current.translation())))
            )}
          >
            <IDCard />
          </group>
        </RigidBody>
      </group>
      
      <mesh ref={band}>
        <meshLineGeometry />
        <meshLineMaterial
          color="#e0e0e0"
          depthTest={false}
          resolution={[width, height]}
          lineWidth={1.5}
          dashArray={0.1}
          dashRatio={0.5}
          transparent={true}
          opacity={0.95}
        />
      </mesh>
    </>
  )
}

// Main 3D Scene Component
const IDCard3D = () => {
  return (
    <div className="card3d-container">
      <Canvas camera={{ position: [0, 0, 13], fov: 25 }}>
        {/* Reduced lighting for darker appearance */}
        <ambientLight intensity={0.5} color="#f5f5dc" />
        <directionalLight
          position={[8, 8, 5]}
          intensity={0.5}
          color="#f5f5dc"
          castShadow
        />
        <directionalLight
          position={[-6, 6, 4]}
          intensity={0.2}
          color="#f0f0f0"
        />
        <pointLight
          position={[0, 0, 12]}
          intensity={0.2}
          color="#f5f5dc"
        />
        <spotLight
          position={[6, 6, 6]}
          intensity={0.1}
          angle={0.4}
          penumbra={0.6}
          color="#f5f5dc"
        />
        {/* Subtle rim lighting */}
        <pointLight
          position={[-8, 0, 8]}
          intensity={0.2}
          color="#e0e0e0"
        />
        <Physics interpolate gravity={[0, -40, 0]} timeStep={1 / 60}>
          <LanyardBand />
        </Physics>
        <Environment preset="warehouse" />
      </Canvas>

    </div>
  )
}

export default IDCard3D
