import React, { useState, useEffect } from 'react'
import { Canvas } from '@react-three/fiber'
import { OrbitControls, useGLTF } from '@react-three/drei'
import * as THREE from 'three'
import './PokemonViewer.css'

// Error Boundary for 3D Model Loading
class Model3DErrorBoundary extends React.Component {
  constructor(props) {
    super(props)
    this.state = { hasError: false, error: null }
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error }
  }

  componentDidCatch(error, errorInfo) {
    console.error('3D Model Error:', error, errorInfo)
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="model-error-fallback">
          <div className="error-icon">🚫</div>
          <div className="error-title">3D Model Not Available</div>
          <div className="error-message">
            This Pokemon form doesn't have a 3D model available.
            <br />
            Try switching to 2D view or select a different form.
          </div>
        </div>
      )
    }

    return this.props.children
  }
}

// 3D Model Component with Error Handling
const Pokemon3DModel = ({ modelUrl, pokemonName, onError }) => {
  const { scene, error } = useGLTF(modelUrl)

  if (error) {
    console.error('Error loading 3D model:', error)
    onError && onError(error)
    return null
  }

  // Clone the scene to avoid modifying the original
  const clonedScene = scene.clone()

  // Center the model and ensure it faces forward
  clonedScene.position.set(0, 0, 0)
  clonedScene.rotation.set(0, 0, 0)

  // Calculate bounding box to center the model properly
  const box = new THREE.Box3().setFromObject(clonedScene)
  const center = box.getCenter(new THREE.Vector3())
  const size = box.getSize(new THREE.Vector3())

  // Center the model horizontally and adjust vertical positioning
  clonedScene.position.x = -center.x
  clonedScene.position.y = -center.y + (size.y * 0.1) // Slightly lower to account for camera being above
  clonedScene.position.z = -center.z

  // Scale based on the largest dimension to fit nicely, with some padding
  const maxDim = Math.max(size.x, size.y, size.z)
  const scale = maxDim > 0 ? 1.8 / maxDim : 1 // Slightly smaller scale for better fit

  return (
    <primitive
      object={clonedScene}
      scale={[scale, scale, scale]}
      position={[0, 0, 0]}
    />
  )
}

const PokemonViewer = ({ pokemonName, pokemonData }) => {
  const [imageLoaded, setImageLoaded] = useState(false)
  const [imageError, setImageError] = useState(false)
  const [pokemon3DData, setPokemon3DData] = useState(null)
  const [modelLoading, setModelLoading] = useState(true)
  const [modelError, setModelError] = useState(false)
  const [use3D, setUse3D] = useState(true)
  const [selectedForm, setSelectedForm] = useState('regular')
  const [availableForms, setAvailableForms] = useState([])
  const [currentModelError, setCurrentModelError] = useState(false)

  // Fetch 3D model data
  useEffect(() => {
    const fetch3DData = async () => {
      if (!pokemonData?.id) return

      try {
        setModelLoading(true)
        const response = await fetch('https://pokemon-3d-api.onrender.com/v1/pokemon')
        const data = await response.json()
        const pokemon3D = data.find(p => p.id === pokemonData.id)

        if (pokemon3D && pokemon3D.forms && pokemon3D.forms.length > 0) {
          setPokemon3DData(pokemon3D)

          // Extract available forms and set up form selection
          // Filter out forms with invalid or missing model URLs
          const forms = pokemon3D.forms
            .filter(form => form.model && form.model.trim() !== '')
            .map(form => ({
              name: form.formName,
              displayName: getFormDisplayName(form.formName),
              model: form.model
            }))

          if (forms.length > 0) {
            setAvailableForms(forms)

            // Set default form (prefer regular, then first available)
            const defaultForm = forms.find(form => form.name === 'regular') || forms[0]
            setSelectedForm(defaultForm.name)

            setModelError(false)
          } else {
            // No valid forms found
            setModelError(true)
            setUse3D(false)
          }
        } else {
          setModelError(true)
          setUse3D(false)
        }
      } catch (error) {
        console.error('Error fetching 3D data:', error)
        setModelError(true)
        setUse3D(false)
      } finally {
        setModelLoading(false)
      }
    }

    fetch3DData()
  }, [pokemonData?.id])

  // Reset model error when form changes
  useEffect(() => {
    setCurrentModelError(false)
  }, [selectedForm])

  const handleImageLoad = () => {
    setImageLoaded(true)
  }

  const handleImageError = () => {
    setImageError(true)
    setImageLoaded(true)
  }

  // Get high-quality Pokemon artwork
  const getArtworkUrl = (id) => {
    return `https://raw.githubusercontent.com/PokeAPI/sprites/master/sprites/pokemon/other/official-artwork/${id}.png`
  }

  // Helper function to get display names for forms
  const getFormDisplayName = (formName) => {
    const formDisplayNames = {
      'regular': '🎮 Regular',
      'shiny': '✨ Shiny',
      'mega': '🔥 Mega',
      'megashiny': '🔥✨ Mega Shiny',
      'gigantamax': '⚡ Gigantamax',
      'alolan': '🌺 Alolan',
      'galarian': '⚔️ Galarian',
      'hisuian': '🏔️ Hisuian',
      'primal': '🌋 Primal',
      'origin': '🌌 Origin',
      'shadow': '👤 Shadow',
      'fusion': '🔗 Fusion',
      'unique': '🌟 Unique'
    }
    return formDisplayNames[formName] || `🎯 ${formName.charAt(0).toUpperCase() + formName.slice(1)}`
  }

  // Validate if a URL is accessible (basic validation)
  const isValidModelUrl = (url) => {
    if (!url || typeof url !== 'string' || url.trim() === '') return false
    try {
      new URL(url)
      return url.includes('.glb') || url.includes('.gltf')
    } catch {
      return false
    }
  }

  // Get 3D model URL for selected form with validation
  const get3DModelUrl = () => {
    if (!pokemon3DData || !availableForms.length) return null

    const selectedFormData = availableForms.find(form => form.name === selectedForm)
    if (selectedFormData && isValidModelUrl(selectedFormData.model)) {
      return selectedFormData.model
    }

    // Fallback to first valid form
    const validForm = availableForms.find(form => isValidModelUrl(form.model))
    return validForm ? validForm.model : null
  }

  const getTypeColor = (type) => {
    const colors = {
      normal: '#A8A878',
      fire: '#F08030',
      water: '#6890F0',
      electric: '#F8D030',
      grass: '#78C850',
      ice: '#98D8D8',
      fighting: '#C03028',
      poison: '#A040A0',
      ground: '#E0C068',
      flying: '#A890F0',
      psychic: '#F85888',
      bug: '#A8B820',
      rock: '#B8A038',
      ghost: '#705898',
      dragon: '#7038F8',
      dark: '#705848',
      steel: '#B8B8D0',
      fairy: '#EE99AC'
    }
    return colors[type] || '#68A090'
  }

  // Handle 3D model loading errors
  const handleModelError = (error) => {
    console.error('3D Model loading failed:', error)
    setCurrentModelError(true)
  }

  // Prevent terminal auto-scroll when clicking in Pokemon viewer
  const handleContainerClick = (e) => {
    e.stopPropagation()
    e.preventDefault()
  }

  return (
    <div
      className="pokemon-viewer-container"
      onClick={handleContainerClick}
      onMouseDown={handleContainerClick}
    >
      <div className="pokemon-header">
        <h3>🎮 Pokemon Viewer - {pokemonName}</h3>
        <span className="pokemon-id">#{pokemonData?.id?.toString().padStart(3, '0') || 'Loading...'}</span>
      </div>
      
      <div className="pokemon-display">
        <div className="pokemon-model-container">
          <div className="view-toggle">
            <button
              className={`toggle-btn ${use3D ? 'active' : ''}`}
              onClick={(e) => {
                e.stopPropagation()
                e.preventDefault()
                setUse3D(true)
              }}
              onMouseDown={(e) => e.stopPropagation()}
              disabled={modelError}
            >
              🎮 3D Model
            </button>
            <button
              className={`toggle-btn ${!use3D ? 'active' : ''}`}
              onClick={(e) => {
                e.stopPropagation()
                e.preventDefault()
                setUse3D(false)
              }}
              onMouseDown={(e) => e.stopPropagation()}
            >
              🖼️ 2D Image
            </button>
          </div>

          {/* Form Selector */}
          {availableForms.length > 1 && (
            <div className="form-selector">
              <label className="form-label">🎭 Pokemon Form:</label>
              <select
                className="form-dropdown"
                value={selectedForm}
                onChange={(e) => {
                  e.stopPropagation()
                  setSelectedForm(e.target.value)
                }}
                onClick={(e) => e.stopPropagation()}
                onMouseDown={(e) => e.stopPropagation()}
              >
                {availableForms.map((form) => (
                  <option key={form.name} value={form.name}>
                    {form.displayName}
                  </option>
                ))}
              </select>
            </div>
          )}

          {use3D && !modelError ? (
            <div className="model-3d-container">
              {modelLoading ? (
                <div className="loading-indicator">
                  <div className="loading-spinner">🔄</div>
                  <div className="loading-text">Loading 3D Model...</div>
                </div>
              ) : currentModelError || !get3DModelUrl() ? (
                <div className="model-error-fallback">
                  <div className="error-icon">🚫</div>
                  <div className="error-title">3D Model Not Available</div>
                  <div className="error-message">
                    This Pokemon form doesn't have a 3D model available.
                    <br />
                    Try switching to 2D view or select a different form.
                  </div>
                  <button
                    className="error-retry-btn"
                    onClick={(e) => {
                      e.stopPropagation()
                      setUse3D(false)
                    }}
                  >
                    Switch to 2D View
                  </button>
                </div>
              ) : (
                <Model3DErrorBoundary>
                  <Canvas
                    camera={{
                      position: [0, 2.5, 7],
                      fov: 90,
                      near: 0.1,
                      far: 1000
                    }}
                    style={{ width: '100%', height: '450px' }}
                  >
                    <ambientLight intensity={0.6} />
                    <directionalLight position={[5, 5, 5]} intensity={0.8} />
                    <directionalLight position={[-5, -5, 5]} intensity={0.4} />
                    <Pokemon3DModel
                      modelUrl={get3DModelUrl()}
                      pokemonName={pokemonName}
                      onError={handleModelError}
                    />
                    <OrbitControls
                      enablePan={true}
                      enableZoom={true}
                      enableRotate={true}
                      autoRotate={true}
                      autoRotateSpeed={1.5}
                      minDistance={0.5}
                      maxDistance={350}
                      target={[0, 0.2, 0]}
                      enableDamping={true}
                      dampingFactor={0.05}
                      zoomSpeed={1.2}
                      rotateSpeed={0.8}
                      panSpeed={0.8}
                    />
                  </Canvas>
                </Model3DErrorBoundary>
              )}
            </div>
          ) : (
            <div className="pokemon-image-container">
              {!imageLoaded && !imageError && (
                <div className="loading-indicator">
                  <div className="loading-spinner">⚡</div>
                  <div className="loading-text">Loading Pokemon...</div>
                </div>
              )}

              {!imageError ? (
                <img
                  src={getArtworkUrl(pokemonData?.id)}
                  alt={pokemonName}
                  className={`pokemon-artwork ${imageLoaded ? 'loaded' : ''}`}
                  onLoad={handleImageLoad}
                  onError={handleImageError}
                />
              ) : (
                <div className="error-fallback">
                  <div className="error-icon">❌</div>
                  <div className="error-text">Pokemon artwork not available</div>
                </div>
              )}
            </div>
          )}
        </div>

        <div className="pokemon-info">
          <div className="pokemon-types">
            {pokemonData?.types?.map((type, index) => (
              <span 
                key={index} 
                className="type-badge"
                style={{ backgroundColor: getTypeColor(type.type.name) }}
              >
                {type.type.name}
              </span>
            ))}
          </div>
        </div>
      </div>

      <div className="pokemon-stats">
        <div className="stat-row">
          <span className="stat-label">📊 Type:</span>
          <span className="stat-value">
            {pokemonData?.types?.map(type => type.type.name).join(', ') || 'Loading...'}
          </span>
        </div>
        <div className="stat-row">
          <span className="stat-label">⚖️ Weight:</span>
          <span className="stat-value">
            {pokemonData?.weight ? `${(pokemonData.weight / 10).toFixed(1)} kg` : 'Loading...'}
          </span>
        </div>
        <div className="stat-row">
          <span className="stat-label">📏 Height:</span>
          <span className="stat-value">
            {pokemonData?.height ? `${(pokemonData.height / 10).toFixed(1)} m` : 'Loading...'}
          </span>
        </div>
        <div className="stat-row">
          <span className="stat-label">⚡ Base Experience:</span>
          <span className="stat-value">
            {pokemonData?.base_experience || 'Unknown'}
          </span>
        </div>
      </div>

      <div className="pokemon-abilities">
        <h4>🎯 Abilities:</h4>
        <div className="abilities-list">
          {pokemonData?.abilities?.map((ability, index) => (
            <span key={index} className="ability-badge">
              {ability.ability.name.replace('-', ' ')}
              {ability.is_hidden && <span className="hidden-tag">(Hidden)</span>}
            </span>
          ))}
        </div>
      </div>

      <div className="pokemon-base-stats">
        <h4>📈 Base Stats:</h4>
        <div className="stats-grid">
          {pokemonData?.stats?.map((stat, index) => (
            <div key={index} className="stat-item">
              <span className="stat-name">{stat.stat.name.replace('-', ' ')}</span>
              <div className="stat-bar">
                <div
                  className="stat-fill"
                  style={{ width: `${Math.min(stat.base_stat / 2, 100)}%` }}
                ></div>
                <span className="stat-number">{stat.base_stat}</span>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Available Forms Information */}
      {availableForms.length > 0 && (
        <div className="pokemon-forms-info">
          <h4>🎭 Available Forms ({availableForms.length}):</h4>
          <div className="forms-grid">
            {availableForms.map((form) => (
              <div
                key={form.name}
                className={`form-card ${selectedForm === form.name ? 'active' : ''}`}
                onClick={(e) => {
                  e.stopPropagation()
                  e.preventDefault()
                  setSelectedForm(form.name)
                }}
                onMouseDown={(e) => e.stopPropagation()}
              >
                <span className="form-name">{form.displayName}</span>
                {selectedForm === form.name && <span className="current-indicator">• Current</span>}
              </div>
            ))}
          </div>
          <div className="forms-description">
            <p>💡 <strong>Tip:</strong> Click on any form above or use the dropdown to switch between different Pokemon forms!</p>
          </div>
        </div>
      )}
    </div>
  )
}

export default PokemonViewer
